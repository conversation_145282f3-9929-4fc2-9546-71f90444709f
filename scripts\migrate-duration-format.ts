import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateDurationFormat() {
  try {
    console.log('Migrating duration format from old to new...');

    // Mapping dari format lama ke format baru
    const oldToNewMapping: Record<string, string> = {
      '1x8_HOURS': '8_JAM',
      '2x8_HOURS': '16_JAM',
      '3x8_HOURS': '24_JAM',
      '4x8_HOURS': '32_JAM',
      '5x8_HOURS': '40_JAM',
      '6x8_HOURS': '48_JAM',
      '7x8_HOURS': '56_JAM',
      '8x8_HOURS': '64_JAM',
      '9x8_HOURS': '72_JAM',
      '10x8_HOURS': '80_JAM'
    };

    // Get all rentals
    const rentals = await prisma.rental.findMany({
      select: {
        id: true,
        duration: true
      }
    });

    console.log(`Found ${rentals.length} rentals to check`);

    let updatedCount = 0;

    for (const rental of rentals) {
      if (rental.duration && rental.duration in oldToNewMapping) {
        const newDuration = oldToNewMapping[rental.duration];
        
        await prisma.rental.update({
          where: { id: rental.id },
          data: { duration: newDuration }
        });
        
        console.log(`Updated rental ${rental.id}: "${rental.duration}" -> "${newDuration}"`);
        updatedCount++;
      }
    }

    console.log(`Successfully migrated ${updatedCount} rentals to new duration format`);

    // Show summary of current durations
    const currentDurations = await prisma.rental.groupBy({
      by: ['duration'],
      _count: {
        duration: true
      }
    });

    console.log('\nCurrent duration distribution:');
    console.log('==============================');
    currentDurations.forEach(item => {
      console.log(`${item.duration}: ${item._count.duration} rentals`);
    });

  } catch (error) {
    console.error('Error migrating duration format:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateDurationFormat();
