import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestRentalsWithVariousDurations() {
  try {
    // Get an existing user and product
    const user = await prisma.user.findFirst({
      where: { role: 'USER' }
    });

    const products = await prisma.product.findMany();

    if (!user || products.length === 0) {
      console.log('No user or products found');
      return;
    }

    // Create test rentals with different durations
    const durations = ['8_JAM', '16_JAM', '40_JAM', '64_JAM'];
    const statuses = ['PENDING', 'CONFIRMED', 'ACTIVE', 'COMPLETED'];

    for (let i = 0; i < durations.length; i++) {
      const duration = durations[i];
      const status = statuses[i];
      const product = products[i % products.length];

      const rental = await prisma.rental.create({
        data: {
          userId: user.id,
          productId: product.id,
          startDate: new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000), // Different start dates
          endDate: new Date(Date.now() + (i + 2) * 24 * 60 * 60 * 1000),
          arrivalTime: '09:00',
          duration: duration,
          address: `Jl. Test Durasi ${i + 1}, Jakarta`,
          notes: `Test rental dengan durasi ${duration}`,
          quantity: 1,
          location: 'on_site',
          purpose: 'testing',
          status: status as any,
          amount: 500000 + (i * 100000),
          payment: {
            create: {
              userId: user.id,
              amount: 500000 + (i * 100000),
              deposit: 250000 + (i * 50000),
              remaining: 250000 + (i * 50000),
              status: 'DEPOSIT_PENDING',
              transactionId: `TEST-DUR-${Date.now()}-${i}`
            }
          }
        },
        include: {
          product: true,
          user: true,
          payment: true
        }
      });

      console.log(`Created rental ${i + 1}:`);
      console.log(`  ID: ${rental.id}`);
      console.log(`  Duration: ${rental.duration}`);
      console.log(`  Status: ${rental.status}`);
      console.log(`  Product: ${rental.product.name}`);
      console.log('');
    }

    console.log('All test rentals created successfully!');

  } catch (error) {
    console.error('Error creating test rentals:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestRentalsWithVariousDurations();
