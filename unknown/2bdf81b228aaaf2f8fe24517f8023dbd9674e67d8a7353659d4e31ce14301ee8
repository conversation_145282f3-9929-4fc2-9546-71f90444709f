"use client";

import { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { useIsClient } from "@/lib/hooks/use-is-client";

import { SoundButton } from "@/app/components/ui/sound-button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Textarea } from "@/app/components/ui/textarea";
import { Popover, PopoverContent, PopoverTrigger } from "@/app/components/ui/popover";
import { LuCalendar, LuClock, LuAlarmClock, LuMapPin, LuDollarSign, LuCheck, LuUser, LuPhone, LuArrowRight, LuArrowLeft, LuInfo } from "react-icons/lu";
import { getTimeOptions } from "@/lib/utils/format";
import { type RentalDuration } from "@/lib/utils/constants";
import { getDurationOptions, getDurationHours } from "@/lib/utils/duration";
import { useToast } from "@/lib/hooks/use-toast";
import { useSoundToast } from "@/app/components/ui/sound-toast";
import dynamic from 'next/dynamic';
import { motion } from "framer-motion";

// Import MapLibreUi secara dinamis untuk menghindari error saat server-side rendering
const MapLibreUi = dynamic(() => import('@/app/components/map/MapLibreUi'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-[350px] bg-gray-100 rounded-md flex items-center justify-center">
      <p className="text-gray-500">Memuat peta...</p>
    </div>
  ),
});

// Interface untuk penggunaan dengan product langsung
interface ProductProp {
  product: {
    id: string;
    name: string;
    price: number;
    stock?: number;
  };
  startDate?: never;
  setStartDate?: never;
  endDate?: never;
  setEndDate?: never;
  duration?: never;
  setDuration?: never;
  address?: never;
  setAddress?: never;
  notes?: never;
  setNotes?: never;
  arrivalTime?: never;
  setArrivalTime?: never;
  coordinates?: never;
  setCoordinates?: never;
  formErrors?: never;
  validateAddress?: never;
}

// Interface untuk penggunaan dengan props lengkap
interface RentalFormProps {
  product?: never;
  startDate: string;
  setStartDate: (date: string) => void;
  endDate: string;
  setEndDate: (date: string) => void;
  duration: RentalDuration | "";
  setDuration: (duration: RentalDuration | "") => void;
  address: string;
  setAddress: (address: string) => void;
  notes: string;
  setNotes: (notes: string) => void;
  arrivalTime: string;
  setArrivalTime: (time: string) => void;
  coordinates: { lat: number, lng: number } | null;
  setCoordinates: (coordinates: { lat: number, lng: number } | null) => void;
  formErrors: { [key: string]: string };
  validateAddress: (address: string) => string | false;
}

type Props = ProductProp | RentalFormProps;

export function RentalFormNew(props: Props) {
  const { showError } = useToast();
  const soundToast = useSoundToast();
  const isClient = useIsClient();

  // Jika props memiliki product, gunakan hook terpisah
  const isProductMode = 'product' in props;

  // State untuk mode product
  const [internalStartDate, setInternalStartDate] = useState(new Date().toISOString().split('T')[0]);
  const [internalEndDate, setInternalEndDate] = useState("");
  const [internalDuration, setInternalDuration] = useState<RentalDuration | "">("");
  const [internalAddress, setInternalAddress] = useState("");
  const [internalNotes, setInternalNotes] = useState("");
  const [internalArrivalTime, setInternalArrivalTime] = useState("09:00");
  const [internalCoordinates, setInternalCoordinates] = useState<{ lat: number, lng: number } | null>(null);
  const [internalFormErrors] = useState<{ [key: string]: string }>({});
  const [isMapPickerOpen, setIsMapPickerOpen] = useState(false);

  // Effect untuk mengatur body scroll lock saat modal terbuka
  useEffect(() => {
    if (!isClient) return;

    if (isMapPickerOpen) {
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      document.body.classList.add('modal-open');
    } else {
      // Restore body scroll
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.classList.remove('modal-open');
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.classList.remove('modal-open');
    };
  }, [isMapPickerOpen, isClient]);

  // State untuk multi-step form
  const [activeStep, setActiveStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");
  const [totalPrice, setTotalPrice] = useState(0);
  const [phoneError, setPhoneError] = useState("");

  // Destructure berdasarkan mode
  const {
    startDate,
    setStartDate,
    endDate,
    duration,
    setDuration,
    address,
    setAddress,
    notes,
    setNotes,
    arrivalTime,
    setArrivalTime,
    coordinates,
    setCoordinates,
    formErrors,
    validateAddress
  } = isProductMode
      ? {
        startDate: internalStartDate,
        setStartDate: setInternalStartDate,
        endDate: internalEndDate,
        duration: internalDuration,
        setDuration: setInternalDuration,
        address: internalAddress,
        setAddress: setInternalAddress,
        notes: internalNotes,
        setNotes: setInternalNotes,
        arrivalTime: internalArrivalTime,
        setArrivalTime: setInternalArrivalTime,
        coordinates: internalCoordinates,
        setCoordinates: setInternalCoordinates,
        formErrors: internalFormErrors,
        validateAddress: (addr: string) => addr.length >= 10 ? addr : false
      }
      : props as RentalFormProps;

  // Dapatkan opsi waktu untuk dropdown
  const timeOptions = getTimeOptions();

  // Buat opsi durasi menggunakan utility function
  const durationOptions = getDurationOptions();

  // Fungsi validasi nomor WhatsApp
  const validateWhatsAppNumber = (phoneNumber: string): boolean => {
    // Regex untuk nomor WhatsApp Indonesia yang valid
    const whatsappRegex = /^(\+62|62|0)8[1-9][0-9]{6,9}$/;
    return whatsappRegex.test(phoneNumber.replace(/\s/g, ''));
  };

  // Handler untuk perubahan nomor WhatsApp
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPhone(value);

    if (value && !validateWhatsAppNumber(value)) {
      setPhoneError("Format nomor WhatsApp tidak valid (contoh: 08xxxxxxxxxx)");
    } else {
      setPhoneError("");
    }
  };

  // Jika dalam mode product, perbarui tanggal selesai berdasarkan durasi
  useEffect(() => {
    if (isProductMode && startDate && duration) {
      try {
        const rentalDuration = duration as RentalDuration;
        const hours = getDurationHours(rentalDuration);

        // Hitung tanggal selesai berdasarkan durasi
        const start = new Date(startDate);
        const end = new Date(start);
        end.setHours(end.getHours() + hours);
        setInternalEndDate(end.toISOString().split('T')[0]);

        // Hitung total harga
        if (isProductMode) {
          const product = (props as ProductProp).product;
          const days = Math.ceil(hours / 24);
          setTotalPrice(product.price * days);
        }
      } catch (error) {
        console.error("Error updating end date:", error);
      }
    }
  }, [isProductMode, startDate, duration, props]);

  // Fungsi untuk mendapatkan alamat dari lokasi
  const getAddressFromCoordinates = async () => {
    try {
      // Tampilkan modal peta
      setIsMapPickerOpen(true);
    } catch (error) {
      console.error("Error in getAddressFromCoordinates:", error);
      showError("Terjadi kesalahan saat mendapatkan lokasi");
    }
  };

  // Handler ketika lokasi dipilih dari peta
  const handleLocationSelected = (location: { lat: number; lng: number; address: string }) => {
    // Perbarui koordinat
    setCoordinates({ lat: location.lat, lng: location.lng });

    // Langsung perbarui alamat di input utama
    setAddress(location.address);

    // Tampilkan toast untuk konfirmasi lokasi dipilih
    soundToast.success("Lokasi dipilih. Alamat berhasil ditambahkan ke formulir");

    // Dialog peta TIDAK langsung ditutup, biarkan pengguna menutupnya sendiri
    // atau menentukan lokasi lain jika perlu
  };

  // Handler untuk langkah berikutnya
  const handleNextStep = () => {
    if (activeStep === 1) {
      if (!startDate) {
        soundToast.error("Tanggal mulai diperlukan. Silakan pilih tanggal mulai untuk melanjutkan");
        return;
      }

      if (!duration) {
        soundToast.error("Durasi diperlukan. Silakan pilih durasi sewa untuk melanjutkan");
        return;
      }

      setActiveStep(2);
    }
  };

  // Handler untuk kembali ke langkah sebelumnya
  const handlePrevStep = () => {
    setActiveStep(1);
  };

  // Handler submit untuk mode product
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isProductMode) return;

    // Validasi basic
    if (!startDate || !duration || !address || address.trim().length < 10) {
      showError("Formulir tidak valid. Mohon lengkapi semua field yang diperlukan");
      return;
    }

    if (!name || !phone) {
      showError("Informasi kontak tidak valid. Mohon lengkapi nama dan nomor WhatsApp Anda");
      return;
    }

    if (!validateWhatsAppNumber(phone)) {
      showError("Format nomor WhatsApp tidak valid. Gunakan format 08xxxxxxxxxx");
      return;
    }

    setIsSubmitting(true);

    try {
      const product = (props as ProductProp).product;

      // Validasi alamat
      const validatedAddress = validateAddress ? validateAddress(address) : address;
      if (!validatedAddress) {
        showError("Alamat tidak valid. Minimal 10 karakter diperlukan");
        setIsSubmitting(false);
        return;
      }

      // Buat purpose yang memenuhi validasi minimal 10 karakter
      let rentalPurpose = `Penyewaan genset ${product.name} untuk keperluan operasional`;
      if (notes && notes.length > 0) {
        rentalPurpose += ` dengan catatan: ${notes}`;
      }

      // Hitung total harga berdasarkan durasi
      const rentalDuration = duration as RentalDuration;
      const hours = getDurationHours(rentalDuration);
      const shifts = Math.ceil(hours / 8);
      const calculatedTotalPrice = product.price * shifts;

      const orderData = {
        productId: product.id,
        startDate,
        endDate,
        arrivalTime,
        deliveryType: duration,
        deliveryAddress: validatedAddress,
        notes: notes || "",
        purpose: rentalPurpose,
        quantity: 1,
        totalAmount: calculatedTotalPrice,
        downPayment: calculatedTotalPrice * 0.5 // 50% deposit
      };

      // Kirim data ke API
      const response = await fetch('/api/rentals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || 'Gagal membuat pesanan';
        throw new Error(errorMessage);
      }

      const data = await response.json();

      // Set form sebagai sudah disubmit
      setIsSubmitting(false);
      setFormSubmitted(true);

      // Redirect ke halaman deposit payment setelah 2 detik
      setTimeout(() => {
        if (typeof window !== 'undefined') {
          window.location.href = `/user/payments/deposit/${data.id}`;
        }
      }, 2000);

    } catch (error) {
      console.error("Error creating rental:", error);
      showError(error instanceof Error ? error.message : "Gagal membuat pesanan");
      setIsSubmitting(false);
    }
  };

  // Animation variants
  const formVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } }
  };

  const successVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { scale: 1, opacity: 1, transition: { duration: 0.5, type: "spring", stiffness: 100 } }
  };

  // Show loading state during SSR
  if (!isClient) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600"></div>
      </div>
    );
  }

  // Show success message after form submission
  if (formSubmitted) {
    const product = isProductMode ? (props as ProductProp).product : { name: "produk" };

    return (
      <motion.div
        className="text-center py-16 px-4"
        initial="hidden"
        animate="visible"
        variants={successVariants}
      >
        <div className="inline-flex items-center justify-center w-16 h-16 mb-6 rounded-full bg-violet-100 dark:bg-violet-900/30">
          <LuCheck className="h-8 w-8 text-violet-600 dark:text-violet-400" />
        </div>
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Pesanan Berhasil!</h3>
        <p className="text-gray-600 dark:text-gray-300 mb-8 max-w-md mx-auto">
          Terima kasih telah menyewa {product.name}. Kami akan menghubungi Anda segera untuk konfirmasi pesanan.
        </p>
      </motion.div>
    );
  }

  return (
    <form className="space-y-6" onSubmit={handleSubmit}>
      {/* Product Info Banner */}
      {isProductMode && (
        <motion.div
          className="flex items-center gap-4 p-6 bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl border border-violet-200 dark:border-violet-800 shadow-sm"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <div className="bg-white dark:bg-gray-800 rounded-full p-3 shadow-md">
            <LuDollarSign className="h-6 w-6 text-violet-600 dark:text-violet-400 flex-shrink-0" />
          </div>
          <div>
            <h3 className="font-medium text-violet-800 dark:text-violet-300">
              Penyewaan {(props as ProductProp).product.name}
            </h3>
            <p className="text-sm text-violet-700 dark:text-violet-400">
              Harga sewa: Rp {(props as ProductProp).product.price.toLocaleString("id-ID")} / hari
            </p>
            {totalPrice > 0 && (
              <p className="font-bold text-violet-900 dark:text-violet-200 mt-1">
                Total: Rp {totalPrice.toLocaleString("id-ID")}
              </p>
            )}
          </div>
        </motion.div>
      )}

      {/* Multi-step Form */}
      {activeStep === 1 ? (
        <motion.div
          className="space-y-8"
          initial="hidden"
          animate="visible"
          variants={formVariants}
        >
          <div className="flex justify-between mb-2">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Detail Sewa</h3>
            <div className="flex items-center gap-2">
              <span className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-white text-sm">1</span>
              <span className="w-8 h-1 bg-gray-300 dark:bg-gray-700 rounded-full"></span>
              <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-300 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-sm">2</span>
            </div>
          </div>

          <div className="grid sm:grid-cols-2 gap-6">
            {/* Tanggal Mulai */}
            <div className="space-y-2">
              <Label htmlFor="startDate" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <LuCalendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                Tanggal Mulai Sewa
              </Label>
              <div className="relative">
                <Input
                  id="startDate"
                  type="date"
                  className="w-full h-12 rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 dark:focus:ring-blue-800 dark:focus:ring-opacity-50 pl-10"
                  required
                  min={new Date().toISOString().split("T")[0]}
                  value={startDate}
                  onChange={(e) => {
                    try {
                      const selectedDate = new Date(e.target.value);
                      const today = new Date();
                      today.setHours(0, 0, 0, 0); // Reset waktu ke awal hari

                      if (selectedDate >= today) {
                        setStartDate(e.target.value);
                      } else {
                        showError("Tanggal tidak valid. Tanggal mulai tidak boleh di masa lalu");
                      }
                    } catch (error) {
                      console.error("Error parsing date:", error);
                      showError("Format tanggal tidak valid. Silakan pilih tanggal dengan format yang benar");
                    }
                  }}
                />
                <LuCalendar className="absolute left-3 top-3.5 h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">Minimal H+1 dari hari ini</p>
            </div>

            {/* Tanggal Selesai */}
            <div className="space-y-2">
              <Label htmlFor="endDate" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <LuCalendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                Tanggal Selesai Sewa
              </Label>
              <div className="relative">
                <Input
                  id="endDate"
                  type="date"
                  className="w-full h-12 rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 dark:focus:ring-blue-800 dark:focus:ring-opacity-50 pl-10 bg-gray-100 dark:bg-gray-700"
                  value={endDate}
                  disabled
                />
                <LuCalendar className="absolute left-3 top-3.5 h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <p className="text-xs text-teal-600 dark:text-teal-400">
                Tanggal selesai otomatis disesuaikan berdasarkan durasi yang dipilih
              </p>
            </div>

            {/* Waktu Kedatangan */}
            <div className="space-y-2">
              <Label htmlFor="arrival_time" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <LuClock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                Waktu Kedatangan
              </Label>
              <div className="relative">
                <Select value={arrivalTime} onValueChange={setArrivalTime}>
                  <SelectTrigger className="w-full h-12 pl-10 rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 dark:focus:ring-blue-800 dark:focus:ring-opacity-50">
                    <SelectValue placeholder="Pilih waktu kedatangan" />
                  </SelectTrigger>
                  <SelectContent>
                    {timeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <LuAlarmClock className="absolute left-3 top-3.5 h-5 w-5 text-blue-600 dark:text-blue-400 pointer-events-none" />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Pilih waktu kedatangan yang diinginkan
              </p>
            </div>

            {/* Durasi Sewa */}
            <div className="space-y-2">
              <Label htmlFor="duration" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <LuClock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                Durasi Sewa
              </Label>
              <Select value={duration} onValueChange={(value) => setDuration(value as RentalDuration | "")}>
                <SelectTrigger className="w-full h-12 rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 dark:focus:ring-blue-800 dark:focus:ring-opacity-50">
                  <SelectValue placeholder="Pilih durasi" />
                </SelectTrigger>
                <SelectContent>
                  {durationOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {!duration && (
                <p className="text-xs text-rose-500">
                  Durasi sewa harus dipilih
                </p>
              )}
            </div>
          </div>

          {/* Navigation Button */}
          <div className="pt-4 flex justify-end">
            <SoundButton
              type="button"
              className="bg-gradient-to-r from-violet-600 to-indigo-500 hover:from-violet-700 hover:to-indigo-600 dark:from-violet-500/90 dark:to-indigo-600/90 dark:hover:from-violet-500 dark:hover:to-indigo-700 text-white dark:text-white shadow-sm py-2.5 px-6 rounded-lg hover:shadow-lg transform transition-all duration-200 hover:-translate-y-0.5 flex items-center gap-2 w-full sm:w-auto justify-center sm:justify-start"
              onClick={handleNextStep}
              soundType="click"
            >
              Lanjutkan <LuArrowRight className="h-4 w-4" />
            </SoundButton>
          </div>
        </motion.div>
      ) : (
        <motion.div
          className="space-y-8"
          initial="hidden"
          animate="visible"
          variants={formVariants}
        >
          <div className="flex justify-between mb-2">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Informasi Kontak</h3>
            <div className="flex items-center gap-2">
              <span
                className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-300 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-sm cursor-pointer hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors"
                onClick={handlePrevStep}
              >1</span>
              <span className="w-8 h-1 bg-gray-300 dark:bg-gray-700 rounded-full"></span>
              <span className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-white text-sm">2</span>
            </div>
          </div>

          <div className="space-y-6">
            <div className="grid sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                  <LuUser className="h-4 w-4 text-violet-600 dark:text-violet-400" />
                  Nama Lengkap
                </Label>
                <div className="relative">
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full h-12 rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 dark:focus:ring-blue-800 dark:focus:ring-opacity-50 pl-10"
                    required
                  />
                  <LuUser className="absolute left-3 top-3.5 h-5 w-5 text-violet-600 dark:text-violet-400" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                  <LuPhone className="h-4 w-4 text-violet-600 dark:text-violet-400" />
                  Nomor WhatsApp <span className="text-rose-500 ml-1">*</span>
                  <Popover>
                    <PopoverTrigger asChild>
                      <button
                        type="button"
                        className="ml-1 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        title="Mengapa nomor WhatsApp diperlukan?"
                      >
                        <LuInfo className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      </button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <LuPhone className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          <h4 className="font-medium text-gray-900 dark:text-gray-100">
                            Mengapa nomor WhatsApp diperlukan?
                          </h4>
                        </div>
                        <ul className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                          <li className="flex items-start gap-2">
                            <span className="text-blue-600 dark:text-blue-400 mt-1">•</span>
                            <span>Pengiriman invoice langsung ke WhatsApp Anda</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-blue-600 dark:text-blue-400 mt-1">•</span>
                            <span>Konfirmasi pesanan dan update status rental</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-blue-600 dark:text-blue-400 mt-1">•</span>
                            <span>Komunikasi cepat untuk koordinasi pengiriman</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-blue-600 dark:text-blue-400 mt-1">•</span>
                            <span>Notifikasi pembayaran dan reminder</span>
                          </li>
                        </ul>
                      </div>
                    </PopoverContent>
                  </Popover>
                </Label>
                <div className="relative">
                  <Input
                    id="phone"
                    type="tel"
                    value={phone}
                    onChange={handlePhoneChange}
                    className={`w-full h-12 rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 dark:focus:ring-blue-800 dark:focus:ring-opacity-50 pl-10 ${phoneError ? 'border-rose-500 focus:border-rose-500' : ''}`}
                    required
                    placeholder="08xxxxxxxxxx (WhatsApp)"
                  />
                  <LuPhone className="absolute left-3 top-3.5 h-5 w-5 text-violet-600 dark:text-violet-400" />
                </div>
                {phoneError && (
                  <p className="text-xs text-rose-500 mt-1">
                    {phoneError}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2 address-section-spacing">
              <Label htmlFor="address" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <LuMapPin className="h-4 w-4 text-violet-600 dark:text-violet-400" />
                Alamat Pengiriman <span className="text-rose-500 ml-1">*</span>
              </Label>

              {/* Address Textarea Field */}
              <div className="relative address-textarea-container">
                <Textarea
                  id="address"
                  placeholder="Masukkan alamat lengkap pengiriman (min. 10 karakter)&#10;&#10;Contoh:&#10;Jalan Raya Mataram No. 123&#10;Kelurahan Mataram Timur&#10;Kecamatan Mataram, Kota Mataram&#10;Nusa Tenggara Barat 83127"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  required
                  rows={4}
                  className={`address-input-field w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 dark:focus:ring-blue-800 dark:focus:ring-opacity-50 pl-10 pt-3 resize-none transition-all duration-200 ${(!address || address.trim().length < 10) ? 'border-rose-500 focus:border-rose-500' : ''}`}
                  onBlur={(e) => {
                    const validatedAddress = validateAddress(e.target.value);
                    if (validatedAddress) {
                      setAddress(validatedAddress);
                    }
                  }}
                />
                <LuMapPin className="address-textarea-icon absolute left-3 top-3 h-5 w-5 text-violet-600 dark:text-violet-400" />
              </div>

              {/* Map Picker Button - Now positioned below the input */}
              <div className="pt-1 address-button-container">
                <SoundButton
                  variant="outline"
                  className="address-map-button w-full sm:w-auto flex items-center justify-center gap-2 h-10 bg-gradient-to-r from-violet-50 to-indigo-50 hover:from-violet-100 hover:to-indigo-100 dark:from-violet-950/40 dark:to-indigo-950/40 dark:hover:from-violet-900/60 dark:hover:to-indigo-900/60 text-violet-800 dark:text-violet-300 border border-violet-200 dark:border-violet-800 transition-all duration-200 hover:shadow-md"
                  type="button"
                  onClick={getAddressFromCoordinates}
                  soundType="click"
                >
                  <LuMapPin className="h-4 w-4" />
                  <span className="font-medium">Pilih di Peta</span>
                </SoundButton>
              </div>

              {/* Error Message */}
              {formErrors.address && (
                <p className="text-xs text-rose-500 mt-1">
                  {formErrors.address}
                </p>
              )}

              {/* Coordinates Display */}
              {coordinates && (
                <div className="coordinates-display-mobile mt-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-md border border-green-200 dark:border-green-800">
                  <p className="text-xs text-green-600 dark:text-green-400 flex items-center gap-1">
                    <LuMapPin className="h-3 w-3 flex-shrink-0" />
                    <span className="font-mono">
                      Koordinat: {coordinates.lat.toFixed(6)}, {coordinates.lng.toFixed(6)}
                    </span>
                  </p>
                </div>
              )}
            </div>

            {/* Map picker modal - Kotak persegi dengan Portal */}
            {isMapPickerOpen && isClient && createPortal(
              <div
                className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-2 sm:p-4 md:p-2 map-picker-modal"
                style={{
                  position: 'fixed',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  zIndex: 9999,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                onClick={(e) => {
                  // Klik pada overlay tidak menutup dialog agar tidak tertutup saat interaksi peta
                  e.stopPropagation();
                }}
              >
                <div
                  className="w-full h-full sm:w-[600px] sm:h-[700px] md:w-[85vw] md:max-w-[800px] md:h-[92vh] md:max-h-[95vh] md:min-h-[800px] lg:w-[90vh] lg:h-[95vh] bg-white dark:bg-gray-800 rounded-none sm:rounded-xl overflow-hidden shadow-2xl flex flex-col map-picker-content"
                  onClick={(e) => {
                    // Mencegah event menjalar ke overlay
                    e.stopPropagation();
                  }}
                >
                  {/* Header - Responsive */}
                  <div className="flex-shrink-0 p-3 sm:p-4 md:p-5 lg:p-4 bg-gradient-to-r from-violet-600 to-indigo-500 dark:from-violet-800 dark:to-indigo-900 text-white flex justify-between items-center sm:rounded-t-xl">
                    <h3 className="text-base sm:text-lg md:text-xl lg:text-lg font-semibold">Pilih Lokasi di Peta</h3>
                    <button
                      type="button"
                      onClick={(e) => {
                        // Hentikan propagasi event untuk mencegah penutupan yang tidak diinginkan
                        e.stopPropagation();
                        setIsMapPickerOpen(false);
                      }}
                      className="text-white hover:text-gray-200 p-1.5 sm:p-2 md:p-2.5 lg:p-2 rounded-full hover:bg-white/10 transition-colors text-xl sm:text-2xl md:text-3xl lg:text-2xl leading-none"
                    >
                      &times;
                    </button>
                  </div>

                  {/* Content - Peta responsif */}
                  <div className="flex-1 overflow-hidden p-2 sm:p-3 md:p-5 lg:p-3 min-h-0 map-picker-controls">
                    <div className="w-full h-full map-picker-map-container">
                      <MapLibreUi
                        onSelectLocation={handleLocationSelected}
                        defaultAddress={address}
                      />
                    </div>
                  </div>

                  {/* Footer - Hanya tombol konfirmasi */}
                  <div className="flex-shrink-0 p-3 sm:p-4 md:p-5 lg:p-4 bg-gray-100 dark:bg-gray-700 border-t dark:border-gray-600 flex justify-center sm:rounded-b-xl">
                    <SoundButton
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsMapPickerOpen(false);
                      }}
                      className="w-full max-w-xs px-4 py-3 md:px-6 md:py-4 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 dark:from-green-600 dark:to-green-500 text-white text-sm md:text-lg font-medium min-h-[44px] md:min-h-[52px] map-picker-button"
                      soundType="success"
                    >
                      Konfirmasi Lokasi
                    </SoundButton>
                  </div>
                </div>
              </div>,
              document.body
            )}

            <div className="space-y-2">
              <Label htmlFor="notes" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <LuMapPin className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                Catatan Tambahan (opsional)
              </Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-violet-500 focus:ring focus:ring-violet-200 dark:focus:ring-violet-800 dark:focus:ring-opacity-50"
                placeholder="Misal: Waktu pengiriman yang diinginkan, akses lokasi, dsb."
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="pt-4 flex flex-col sm:flex-row gap-4 sm:justify-between">
            <SoundButton
              type="button"
              className="text-gray-700 dark:text-violet-300 bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-violet-900/40 py-2.5 px-6 rounded-lg transition-colors flex items-center gap-2 justify-center"
              onClick={handlePrevStep}
              soundType="click"
            >
              <LuArrowLeft className="h-4 w-4 text-gray-500 dark:text-violet-400" /> Kembali
            </SoundButton>

            <SoundButton
              type="submit"
              className="bg-gradient-to-r from-violet-600 to-indigo-500 dark:from-violet-500 dark:to-indigo-600 hover:from-violet-700 hover:to-indigo-600 text-white py-3 px-6 rounded-lg shadow-md hover:shadow-lg transform transition-all duration-200 hover:-translate-y-0.5 flex-1 sm:flex-none"
              disabled={isSubmitting}
              soundType={isSubmitting ? "none" : "success"}
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Memproses...
                </div>
              ) : "Kirim Pesanan"}
            </SoundButton>
          </div>

          <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-4">
            Dengan mengirim pesanan, Anda menyetujui syarat dan ketentuan layanan kami.
          </p>
        </motion.div>
      )}
    </form>
  );
}
