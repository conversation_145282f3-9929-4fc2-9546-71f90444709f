import { z } from "zod";
import { UserRole } from "@prisma/client";
import { whatsappRegex } from "@/lib/utils/whatsapp-validation";

// Schema validasi untuk autentikasi
export const SignInSchema = z.object({
  email: z.string({
    required_error: "Email harus diisi",
    invalid_type_error: "Email harus berupa teks",
  })
    .min(1, { message: "Email harus diisi" })
    .email({ message: "Format email tidak valid" })
    .transform(val => val.toLowerCase()),

  password: z.string({
    required_error: "Password harus diisi",
  })
    .min(6, { message: "Password minimal 6 karakter" })
    .max(100, { message: "Password terlalu panjang" })
});

// Schema validasi untuk registrasi
export const RegisterSchema = z.object({
  name: z.string({
    required_error: "Nama harus diisi",
  })
    .min(1, { message: "Nama harus diisi" })
    .min(3, { message: "Nama minimal 3 karakter" }),

  email: z.string({
    required_error: "Email harus diisi",
  })
    .min(1, { message: "Email harus diisi" })
    .email({ message: "Format email tidak valid" })
    .transform(val => val.toLowerCase()),

  phone: z.string({
    required_error: "Nomor WhatsApp harus diisi",
  })
    .min(1, { message: "Nomor WhatsApp harus diisi" })
    .refine((val) => whatsappRegex.test(val), {
      message: "Format nomor WhatsApp tidak valid (contoh: 08XXXXXXXXXX)"
    }),

  password: z.string({
    required_error: "Password harus diisi",
  })
    .min(1, { message: "Password harus diisi" })
    .min(8, { message: "Password minimal 8 karakter" })
    .regex(/[A-Z]/, { message: "Password harus mengandung huruf besar" })
    .regex(/[a-z]/, { message: "Password harus mengandung huruf kecil" })
    .regex(/[0-9]/, { message: "Password harus mengandung angka" }),

  confirmPassword: z.string({
    required_error: "Konfirmasi password harus diisi",
  })
    .min(1, { message: "Konfirmasi password harus diisi" })
}).refine((data) => data.password === data.confirmPassword, {
  message: "Password tidak sama",
  path: ["confirmPassword"]
});

// Schema validasi untuk user
export const UserSchema = z.object({
  name: z.string().min(1, { message: "Nama harus diisi" }),
  email: z.string().email({ message: "Format email tidak valid" }),
  phone: z.string()
    .refine((val) => !val || whatsappRegex.test(val), {
      message: "Format nomor WhatsApp tidak valid"
    })
    .nullable(),
  role: z.nativeEnum(UserRole, {
    message: "Role tidak valid"
  }).default(UserRole.USER),
});

// Schema validasi untuk update profil
export const ProfileUpdateSchema = z.object({
  name: z.string().min(1, { message: "Nama harus diisi" }),
  phone: z.string()
    .refine((val) => !val || whatsappRegex.test(val), {
      message: "Format nomor WhatsApp tidak valid"
    })
    .nullable(),
}).partial();

// Schema validasi untuk form kontak
export const ContactFormSchema = z.object({
  name: z.string().min(1, { message: "Nama harus diisi" }),
  email: z.string()
    .email({ message: "Format email tidak valid" })
    .min(1, { message: "Email harus diisi" }),
  subject: z.string().min(1, { message: "Subjek harus diisi" }),
  message: z.string()
    .min(10, { message: "Pesan minimal 10 karakter" })
    .max(1000, { message: "Pesan maksimal 1000 karakter" }),
});

// Tipe-tipe yang diekspor
export type SignInInput = z.infer<typeof SignInSchema>;
export type RegisterInput = z.infer<typeof RegisterSchema>;
export type UserInput = z.infer<typeof UserSchema>;
export type ProfileUpdateInput = z.infer<typeof ProfileUpdateSchema>;
export type ContactFormInput = z.infer<typeof ContactFormSchema>;

// Membuat objek schemas untuk ekspor
const schemas = {
  SignInSchema,
  RegisterSchema,
  UserSchema,
  ProfileUpdateSchema,
  ContactFormSchema
};

export default schemas;
