import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestRental() {
  try {
    // First, get an existing user and product
    const user = await prisma.user.findFirst({
      where: { role: 'USER' }
    });

    const product = await prisma.product.findFirst();

    if (!user || !product) {
      console.log('No user or product found. Creating test data...');
      
      // Create a test user if none exists
      const testUser = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '081234567890',
          role: 'USER',
          password: 'hashedpassword' // In real app, this should be properly hashed
        }
      });

      // Create a test product if none exists
      const testProduct = await prisma.product.upsert({
        where: { name: 'Test Generator' },
        update: {},
        create: {
          name: 'Test Generator',
          capacity: 100,
          description: 'Test generator for testing purposes',
          price: 500000,
          overtimeRate: 50000,
          isAvailable: true
        }
      });

      console.log('Test user and product created');
    }

    // Get the user and product again
    const finalUser = await prisma.user.findFirst({ where: { role: 'USER' } });
    const finalProduct = await prisma.product.findFirst();

    if (!finalUser || !finalProduct) {
      throw new Error('Could not find or create user/product');
    }

    // Create a test rental with PENDING status
    const rental = await prisma.rental.create({
      data: {
        userId: finalUser.id,
        productId: finalProduct.id,
        startDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        endDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        arrivalTime: '09:00',
        duration: '24_JAM', // 24 Jam
        address: 'Jl. Test No. 123, Jakarta',
        notes: 'Test rental for testing dropdown menu',
        quantity: 1,
        location: 'on_site',
        purpose: 'testing',
        status: 'PENDING',
        amount: 1000000,
        payment: {
          create: {
            userId: finalUser.id,
            amount: 1000000,
            deposit: 500000,
            remaining: 500000,
            status: 'DEPOSIT_PENDING',
            transactionId: `TEST-${Date.now()}`
          }
        }
      },
      include: {
        product: true,
        user: true,
        payment: true
      }
    });

    console.log('Test rental created successfully:');
    console.log(`ID: ${rental.id}`);
    console.log(`Status: ${rental.status}`);
    console.log(`User: ${rental.user.name}`);
    console.log(`Product: ${rental.product.name}`);
    console.log(`Amount: ${rental.amount}`);

  } catch (error) {
    console.error('Error creating test rental:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestRental();
