"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { <PERSON><PERSON>ser, LuPhone, LuMapPin, LuMail, LuShield } from "react-icons/lu";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Textarea } from "@/app/components/ui/textarea";
import { Label } from "@/app/components/ui/label";
import { SubmitButton } from "@/app/components/ui/submit-button";
import { ProfileFormStatus } from "./client";
import { LogoutButton } from "../_components/logout-button";
import { Skeleton } from "@/app/components/ui/skeleton";

// Interface untuk session user dari server
interface UserData {
  id: string;
  name: string;
  email: string;
  phone: string;
  image: string;
  role: string;
  address: string;
  bio: string;
  joinDate: string;
}

// Props untuk ProfileClient
interface ProfileClientProps {
  user: UserData;
  handleProfileUpdate: (formData: FormData) => Promise<void>;
  handlePasswordUpdate: (formData: FormData) => Promise<void>;
}

export function ProfileClient({ user, handleProfileUpdate, handlePasswordUpdate }: ProfileClientProps) {
  // State untuk loading
  const [isLoading, setIsLoading] = useState(true);

  // State untuk menyimpan data profil lengkap (termasuk localStorage)
  const [profileData, setProfileData] = useState({
    id: user.id,
    name: user.name,
    email: user.email,
    phone: user.phone,
    address: user.address,
    bio: user.bio,
    avatarUrl: user.image,
    role: user.role,
    joinDate: user.joinDate
  });

  // Effect untuk memuat data dari localStorage
  useEffect(() => {
    const loadProfileData = async () => {
      try {
        // Minimum loading time untuk memastikan skeleton terlihat
        await new Promise(resolve => setTimeout(resolve, 150));

        const storedData = localStorage.getItem('profileData');
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          console.log('Loading profile data from localStorage:', parsedData);

          // Cek apakah ada URL gambar baru
          const newImageUrl = parsedData.image || user.image;
          console.log('Using image URL:', newImageUrl, 'from:', parsedData.image ? 'localStorage' : 'user.image');

          setProfileData(prev => ({
            ...prev,
            address: parsedData.address || user.address,
            bio: parsedData.bio || user.bio,
            avatarUrl: newImageUrl
          }));
        }
      } catch (error) {
        console.error('Error loading profile data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProfileData();
  }, [user.address, user.bio, user.image]);

  // Effect khusus untuk memantau perubahan pada URL gambar profil
  useEffect(() => {
    // Selalu perbarui avatarUrl ketika prop user.image berubah
    setProfileData(prev => ({
      ...prev,
      avatarUrl: user.image
    }));

    // Simpan URL gambar baru ke localStorage
    try {
      const storedData = localStorage.getItem('profileData');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        localStorage.setItem('profileData', JSON.stringify({
          ...parsedData,
          image: user.image
        }));
      }
    } catch (error) {
      console.error('Error saving image URL to localStorage:', error);
    }
  }, [user.image]);

  // Effect untuk memuat gambar profil dari API setelah form disubmit
  useEffect(() => {
    // Fungsi untuk mengambil URL gambar dari server
    const fetchAvatar = async () => {
      try {
        const response = await fetch('/api/user/avatar');
        if (response.ok) {
          const data = await response.json();
          if (data.image) {
            console.log('Got image URL from API:', data.image);

            // Perbarui state dengan URL gambar baru
            setProfileData(prev => ({
              ...prev,
              avatarUrl: data.image
            }));

            // Simpan URL gambar ke localStorage
            const storedData = localStorage.getItem('profileData');
            const profileData = storedData ? JSON.parse(storedData) : {};
            localStorage.setItem('profileData', JSON.stringify({
              ...profileData,
              image: data.image
            }));
          }
        }
      } catch (error) {
        console.error('Error fetching avatar URL:', error);
      }
    };

    // Buat flag untuk menandakan jika komponen masih terpasang
    let isMounted = true;

    // Fungsi untuk memeriksa URL gambar secara berkala
    const checkAvatar = () => {
      if (isMounted) {
        fetchAvatar();
        // Periksa ulang setiap 3 detik
        setTimeout(checkAvatar, 3000);
      }
    };

    // Mulai pemeriksaan
    checkAvatar();

    // Cleanup ketika komponen di-unmount
    return () => {
      isMounted = false;
    };
  }, []);

  // Tambahkan fungsi untuk memuat gambar dari URL
  function updateAvatar(url: string) {
    console.log('Updating avatar URL to:', url);

    // Fetch gambar untuk memverifikasi aksesibilitas
    fetch(url, { method: 'HEAD' })
      .then(response => {
        if (response.ok) {
          console.log('Avatar URL is accessible, updating state and localStorage');

          // Perbarui localStorage
          const storedData = localStorage.getItem('profileData');
          const profileData = storedData ? JSON.parse(storedData) : {};
          localStorage.setItem('profileData', JSON.stringify({
            ...profileData,
            image: url
          }));

          // Tampilkan pesan sukses
          alert('Foto profil berhasil diupdate. Silakan refresh halaman.');

          // Muat ulang halaman untuk memperbarui UI
          window.location.reload();
        } else {
          console.error('Avatar URL is not accessible:', response.status);
        }
      })
      .catch(error => {
        console.error('Error checking avatar URL:', error);
      });
  }

  // Tampilkan skeleton loading jika masih loading
  if (isLoading) {
    return <ProfileSkeleton />;
  }

  return (
    <>
      {/* Header Section */}
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="relative">
          <h1 className="text-3xl font-bold tracking-tight text-white dark:text-white">Profil Saya</h1>
          <p className="text-white dark:text-gray-400 max-w-xl mt-2">Kelola informasi profil dan keamanan akun Anda</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Informasi Profil</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center space-y-4">
            <div className="relative w-32 h-32 rounded-full overflow-hidden border-4 border-green-500 bg-green-100 flex items-center justify-center">
              {profileData.avatarUrl ? (
                <Image
                  src={profileData.avatarUrl}
                  alt={profileData.name}
                  fill
                  sizes="128px"
                  priority
                  className="object-cover"
                />
              ) : (
                <span className="text-4xl font-bold text-green-600">
                  {profileData.name.charAt(0).toUpperCase()}
                </span>
              )}
            </div>
            <div className="text-center">
              <h3 className="text-xl font-bold">{profileData.name}</h3>
              <p className="text-sm text-muted-foreground">
                Bergabung sejak {profileData.joinDate}
              </p>
            </div>
            <div className="w-full pt-4 space-y-2">
              <div className="flex items-center gap-2">
                <LuMail className="h-4 w-4 text-muted-foreground" />
                <span>{profileData.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <LuPhone className="h-4 w-4 text-muted-foreground" />
                <span>{profileData.phone}</span>
              </div>
              <div className="flex items-center gap-2">
                <LuMapPin className="h-4 w-4 text-muted-foreground" />
                <span>{profileData.address}</span>
              </div>
              <div className="flex items-center gap-2">
                <LuShield className="h-4 w-4 text-muted-foreground" />
                <span>Tipe Akun: {profileData.role === "user" ? "Pengguna" : "Admin"}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Edit Profil</CardTitle>
            <CardDescription>
              Perbarui informasi profil Anda di sini
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form
              action={handleProfileUpdate}
              className="space-y-6"
            >
              <input type="hidden" name="userId" value={user.id} />
              <div className="space-y-2">
                <Label htmlFor="name">Nama Lengkap</Label>
                <div className="relative">
                  <LuUser className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="name"
                    name="name"
                    placeholder="Nama lengkap Anda"
                    defaultValue={profileData.name}
                    className="pl-9"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Alamat Email</Label>
                <div className="relative">
                  <LuMail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="Email Anda"
                    defaultValue={profileData.email !== "-" ? profileData.email : ""}
                    className="pl-9"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Nomor WhatsApp</Label>
                <div className="relative">
                  <LuPhone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="phone"
                    name="phone"
                    placeholder="Nomor WhatsApp Anda (08xxxxxxxxxx)"
                    defaultValue={profileData.phone !== "-" ? profileData.phone : ""}
                    className="pl-9"
                    required
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  Nomor WhatsApp untuk menerima invoice dan komunikasi
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Alamat</Label>
                <div className="relative">
                  <LuMapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="address"
                    name="address"
                    placeholder="Alamat lengkap Anda"
                    defaultValue={profileData.address !== "Alamat belum diisi" ? profileData.address : ""}
                    className="pl-9"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  className="dark:bg-gray-900 dark:text-white"
                  id="bio"
                  name="bio"
                  placeholder="Ceritakan sedikit tentang diri Anda"
                  defaultValue={profileData.bio !== "Bio belum diisi" ? profileData.bio : ""}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="avatar">Foto Profil</Label>
                <Input
                  id="avatar"
                  name="avatar"
                  type="file"
                  accept="image/*"
                />
                <div className="mt-2">
                  <Button
                    type="button"
                    className="text-xs"
                    variant="outline"
                    onClick={() => {
                      // URL gambar profil terbaru dari Vercel Blob Storage
                      const newAvatarUrl = prompt('Masukkan URL gambar profil terbaru:');
                      if (newAvatarUrl && newAvatarUrl.trim() !== '') {
                        updateAvatar(newAvatarUrl.trim());
                      }
                    }}
                  >
                    Update Gambar Manual
                  </Button>
                  <p className="text-xs text-muted-foreground mt-1">
                    Jika gambar tidak muncul setelah upload, gunakan tombol ini untuk memasukkan URL gambar secara manual.
                  </p>
                </div>
              </div>

              <div className="flex justify-between mt-6">
                <Button type="reset" variant="outline">Reset</Button>
                <SubmitButton className="bg-gradient-to-r from-violet-600 to-indigo-500 hover:from-violet-700 hover:to-indigo-600 dark:from-violet-500/90 dark:to-indigo-600/90 dark:hover:from-violet-500 dark:hover:to-indigo-700 text-white dark:text-white shadow-sm">
                  Simpan Perubahan
                </SubmitButton>
                <ProfileFormStatus />
              </div>
            </form>
          </CardContent>
        </Card>

        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Keamanan Akun</CardTitle>
            <CardDescription>
              Kelola keamanan akun Anda
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={handlePasswordUpdate} className="space-y-6">
              <input type="hidden" name="userId" value={user.id} />
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Password Saat Ini</Label>
                <Input
                  id="currentPassword"
                  name="currentPassword"
                  type="password"
                  placeholder="Masukkan password Anda saat ini"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="newPassword">Password Baru</Label>
                <Input
                  id="newPassword"
                  name="newPassword"
                  type="password"
                  placeholder="Masukkan password baru"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Konfirmasi Password</Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  placeholder="Konfirmasi password baru Anda"
                  required
                />
              </div>

              <div className="flex justify-end mt-6">
                <SubmitButton
                  className="bg-gradient-to-r from-violet-600 to-indigo-500 hover:from-violet-700 hover:to-indigo-600 dark:from-violet-500/90 dark:to-indigo-600/90 dark:hover:from-violet-500 dark:hover:to-indigo-700 text-white dark:text-white shadow-sm"
                  pendingText="Memperbarui..."
                >
                  Perbarui Password
                </SubmitButton>
                <ProfileFormStatus />
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Kartu Logout */}
        <Card className="md:col-span-3 mt-8">
          <CardHeader>
            <CardTitle className="text-red-600">Keluar Aplikasi</CardTitle>
            <CardDescription>
              Keluar dari aplikasi Rental Genset
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Klik tombol di bawah untuk keluar dari akun Anda. Anda akan dialihkan ke halaman login.
            </p>
            <LogoutButton className="w-full md:w-auto" />
          </CardContent>
        </Card>
      </div>
    </>
  );
}

// Komponen Skeleton Loading untuk Profile
function ProfileSkeleton() {
  return (
    <>
      {/* Header Section Skeleton */}
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="relative">
          <Skeleton className="h-9 w-48 mb-2 animate-pulse" />
          <Skeleton className="h-4 w-80 animate-pulse" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Profile Info Card */}
        <Card className="md:col-span-1">
          <CardHeader>
            <Skeleton className="h-6 w-32 animate-pulse" />
          </CardHeader>
          <CardContent className="flex flex-col items-center space-y-4">
            {/* Avatar */}
            <Skeleton className="w-32 h-32 rounded-full animate-pulse" />

            {/* User Info */}
            <div className="text-center w-full">
              <Skeleton className="h-6 w-40 mb-2 mx-auto animate-pulse" />
              <Skeleton className="h-4 w-32 mx-auto animate-pulse" />
            </div>

            {/* Contact Details */}
            <div className="w-full pt-4 space-y-3">
              {/* Email */}
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 animate-pulse" />
                <Skeleton className="h-4 w-48 animate-pulse" />
              </div>

              {/* Phone */}
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 animate-pulse" />
                <Skeleton className="h-4 w-32 animate-pulse" />
              </div>

              {/* Address */}
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 animate-pulse" />
                <Skeleton className="h-4 w-56 animate-pulse" />
              </div>

              {/* Role */}
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 animate-pulse" />
                <Skeleton className="h-4 w-28 animate-pulse" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Edit Profile Card */}
        <Card className="md:col-span-2">
          <CardHeader>
            <Skeleton className="h-6 w-24 mb-2 animate-pulse" />
            <Skeleton className="h-4 w-64 animate-pulse" />
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Name Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-24 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Email Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-20 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Phone Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-28 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
              <Skeleton className="h-3 w-80 animate-pulse" />
            </div>

            {/* Address Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-16 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Bio Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-8 animate-pulse" />
              <Skeleton className="h-20 w-full animate-pulse" />
            </div>

            {/* Avatar Upload Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-20 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
              <div className="mt-2">
                <Skeleton className="h-8 w-36 animate-pulse" />
                <Skeleton className="h-3 w-96 mt-1 animate-pulse" />
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-between mt-6">
              <Skeleton className="h-10 w-16 animate-pulse" />
              <Skeleton className="h-10 w-32 animate-pulse" />
            </div>
          </CardContent>
        </Card>

        {/* Security Card */}
        <Card className="md:col-span-3">
          <CardHeader>
            <Skeleton className="h-6 w-32 mb-2 animate-pulse" />
            <Skeleton className="h-4 w-48 animate-pulse" />
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Current Password */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-32 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* New Password */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-24 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Confirm Password */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-36 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end mt-6">
              <Skeleton className="h-10 w-36 animate-pulse" />
            </div>
          </CardContent>
        </Card>

        {/* Logout Card */}
        <Card className="md:col-span-3 mt-8">
          <CardHeader>
            <Skeleton className="h-6 w-32 mb-2 animate-pulse" />
            <Skeleton className="h-4 w-56 animate-pulse" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-80 mb-4 animate-pulse" />
            <Skeleton className="h-10 w-32 animate-pulse" />
          </CardContent>
        </Card>
      </div>
    </>
  );
}
