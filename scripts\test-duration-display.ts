import { RENTAL_DURATIONS } from '../lib/utils/constants';

// Test function to simulate the formatDuration function
function formatDuration(duration: string | null) {
  if (!duration) return '-';

  // Jika duration ada di konstanta RENTAL_DURATIONS, gun<PERSON>n label dari sana
  if (duration in RENTAL_DURATIONS) {
    return RENTAL_DURATIONS[duration as keyof typeof RENTAL_DURATIONS].label;
  }

  // Mapping untuk format lama ke format baru
  const oldToNewMapping: Record<string, string> = {
    '1x8_HOURS': '8_JAM',
    '2x8_HOURS': '16_JAM',
    '3x8_HOURS': '24_JAM',
    '4x8_HOURS': '32_JAM',
    '5x8_HOURS': '40_JAM',
    '6x8_HOURS': '48_JAM',
    '7x8_HOURS': '56_JAM',
    '8x8_HOURS': '64_JAM',
    '9x8_HOURS': '72_JAM',
    '10x8_HOURS': '80_JAM'
  };

  // Cek apakah ini format lama dan konversi ke format baru
  if (duration in oldToNewMapping) {
    const newFormat = oldToNewMapping[duration];
    return RENTAL_DURATIONS[newFormat as keyof typeof RENTAL_DURATIONS].label;
  }

  // Fallback: coba parse dari format lama atau format lain
  if (duration.includes('x8')) {
    const multiplier = parseInt(duration.replace('x8_HOURS', '').replace('_HOURS', ''));
    if (!isNaN(multiplier)) {
      return `${multiplier * 8} Jam`;
    }
  }

  // Jika tidak bisa diparse, tampilkan apa adanya
  return duration;
}

// Test various duration formats
console.log('Testing duration display:');
console.log('=========================');

// Test with new format constants
console.log('8_JAM:', formatDuration('8_JAM'));
console.log('16_JAM:', formatDuration('16_JAM'));
console.log('24_JAM:', formatDuration('24_JAM'));
console.log('32_JAM:', formatDuration('32_JAM'));
console.log('40_JAM:', formatDuration('40_JAM'));

// Test with old format (backward compatibility)
console.log('1x8_HOURS (old):', formatDuration('1x8_HOURS'));
console.log('2x8_HOURS (old):', formatDuration('2x8_HOURS'));
console.log('3x8_HOURS (old):', formatDuration('3x8_HOURS'));

// Test with null/undefined
console.log('null:', formatDuration(null));
console.log('undefined:', formatDuration(undefined as any));

// Test with old formats
console.log('5x8:', formatDuration('5x8'));
console.log('3_HOURS:', formatDuration('3_HOURS'));

// Test with invalid formats
console.log('invalid:', formatDuration('invalid'));
console.log('1 hari:', formatDuration('1 hari'));

console.log('\nAll available durations:');
console.log('========================');
Object.entries(RENTAL_DURATIONS).forEach(([key, value]) => {
  console.log(`${key}: ${value.label} (${value.hours} jam)`);
});
