import { useState, useCallback } from 'react';

/**
 * Hook untuk mendapatkan lokasi pengguna saat ini menggunakan Geolocation API
 * dengan fitur retry dan validasi koordinat untuk akurasi yang lebih baik
 */
const useGeoLocation = () => {
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number; accuracy?: number } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [positionError, setPositionError] = useState<string | null>(null);

  const clearPositionError = useCallback(() => {
    setPositionError(null);
    setError(null);
  }, []);

  // Fungsi untuk memvalidasi apakah koordinat masuk akal untuk Indonesia
  const isValidIndonesianCoordinates = useCallback((lat: number, lng: number): boolean => {
    // Indonesia berada di sekitar:
    // Latitude: -11° hingga 6° (Utara-Selatan)
    // Longitude: 95° hingga 141° (Barat-Timur)
    return lat >= -11 && lat <= 6 && lng >= 95 && lng <= 141;
  }, []);

  // Fungsi untuk mendapatkan posisi dengan retry mechanism
  const getPositionWithRetry = useCallback(async (maxRetries: number = 3): Promise<GeolocationPosition> => {
    let lastError: GeolocationPositionError | null = null;
    let bestPosition: GeolocationPosition | null = null;
    let bestAccuracy = Infinity;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      console.log(`Percobaan mendapatkan lokasi ke-${attempt} dari ${maxRetries}`);

      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          const timeoutMs = attempt === 1 ? 15000 : 20000; // Timeout lebih lama untuk percobaan selanjutnya

          navigator.geolocation.getCurrentPosition(
            resolve,
            reject,
            {
              enableHighAccuracy: true,
              timeout: timeoutMs,
              maximumAge: attempt === 1 ? 0 : 30000 // Izinkan cache untuk percobaan selanjutnya
            }
          );
        });

        const { latitude, longitude, accuracy } = position.coords;

        // Validasi koordinat
        if (!isValidIndonesianCoordinates(latitude, longitude)) {
          console.warn(`Koordinat tidak valid untuk Indonesia: ${latitude}, ${longitude}`);
          lastError = {
            code: 2,
            message: "Koordinat tidak valid untuk Indonesia"
          } as GeolocationPositionError;
          continue;
        }

        // Jika ini adalah posisi pertama yang valid, atau lebih akurat dari sebelumnya
        if (!bestPosition || (accuracy && accuracy < bestAccuracy)) {
          bestPosition = position;
          bestAccuracy = accuracy || Infinity;
          console.log(`Posisi terbaik diperbarui: akurasi ${accuracy}m`);
        }

        // Jika akurasi sudah cukup baik (< 100m), gunakan posisi ini
        if (accuracy && accuracy < 100) {
          console.log(`Akurasi cukup baik (${accuracy}m), menggunakan posisi ini`);
          return position;
        }

        // Jika ini percobaan terakhir, gunakan posisi terbaik yang ada
        if (attempt === maxRetries && bestPosition) {
          console.log(`Menggunakan posisi terbaik dengan akurasi ${bestAccuracy}m`);
          return bestPosition;
        }

      } catch (error) {
        console.error(`Percobaan ke-${attempt} gagal:`, error);
        lastError = error as GeolocationPositionError;

        // Jika ini bukan percobaan terakhir, tunggu sebentar sebelum retry
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    // Jika ada posisi terbaik, gunakan itu
    if (bestPosition) {
      console.log(`Menggunakan posisi terbaik yang tersedia dengan akurasi ${bestAccuracy}m`);
      return bestPosition;
    }

    // Jika semua percobaan gagal, throw error terakhir
    throw lastError || new Error("Gagal mendapatkan lokasi setelah beberapa percobaan");
  }, [isValidIndonesianCoordinates]);

  const getCurrentPosition = useCallback(() => {
    if (!navigator.geolocation) {
      const errorMsg = "Geolokasi tidak didukung di browser Anda.";
      setError(errorMsg);
      setPositionError(errorMsg);
      return Promise.reject(new Error(errorMsg));
    }

    setIsLoading(true);
    setError(null);
    setPositionError(null);

    return getPositionWithRetry(3)
      .then((position) => {
        const { latitude, longitude, accuracy } = position.coords;
        const newCoords = {
          lat: latitude,
          lng: longitude,
          accuracy: accuracy || undefined
        };
        setCoordinates(newCoords);
        setIsLoading(false);
        console.log(`Lokasi berhasil didapatkan: ${latitude}, ${longitude} (akurasi: ${accuracy}m)`);
        return position;
      })
      .catch((error) => {
        console.error("Error mendapatkan geolokasi:", error);
        let errorMessage = "Terjadi kesalahan saat mendapatkan lokasi Anda.";

        if (error.code) {
          switch(error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = "Akses lokasi ditolak. Silakan izinkan akses lokasi di browser Anda dan refresh halaman.";
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = "Informasi lokasi tidak tersedia. Pastikan GPS aktif dan Anda berada di area dengan sinyal yang baik.";
              break;
            case error.TIMEOUT:
              errorMessage = "Waktu permintaan lokasi habis. Coba lagi atau pastikan GPS aktif.";
              break;
          }
        } else if (error.message) {
          errorMessage = error.message;
        }

        setError(errorMessage);
        setPositionError(errorMessage);
        setIsLoading(false);
        throw new Error(errorMessage);
      });
  }, [getPositionWithRetry]);

  return {
    coordinates,
    isLoading,
    error,
    positionError,
    getCurrentPosition,
    clearPositionError,
    setCoordinates
  };
};

export default useGeoLocation; 
