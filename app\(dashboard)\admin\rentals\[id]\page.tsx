import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";
import { redirect, notFound } from "next/navigation";
import Link from "next/link";
import { formatDate } from "@/lib/utils/format";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { Calendar, Clock, MapPin, FileText, User, Phone, Mail, Package, MoreVertical, CheckCircle, X, PlayCircle, Eye } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/app/components/ui/dropdown-menu";

// Pastikan halaman selalu up-to-date
export const dynamic = 'force-dynamic';

// Fungsi untuk mendapatkan nama status dalam bahasa Indonesia
function getStatusName(status: string): string {
  const statusMap: Record<string, string> = {
    'PENDING': 'Menunggu',
    'CONFIRMED': 'Dikonfirmasi',
    'OPERATIONAL': 'Operasional',
    'COMPLETED': 'Selesai',
    'CANCELLED': 'Dibatalkan'
  };

  return statusMap[status] || status;
}

// Fungsi untuk mendapatkan warna status
function getStatusColor(status: string): string {
  const statusColorMap: Record<string, string> = {
    'PENDING': 'bg-yellow-100 text-yellow-800',
    'CONFIRMED': 'bg-blue-100 text-blue-800',
    'OPERATIONAL': 'bg-blue-100 text-blue-800',
    'COMPLETED': 'bg-purple-100 text-purple-800',
    'CANCELLED': 'bg-red-100 text-red-800'
  };

  return statusColorMap[status] || 'bg-gray-100 text-gray-800';
}

export default async function RentalDetailPage({ params }: { params: { id: string } }) {
  const session = await auth();

  // Redirect ke login jika tidak ada session atau bukan admin
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  // Ambil ID rental dari parameter URL (dengan await)
  const { id } = await params;

  // Ambil data rental dari database
  const rental = await prisma.rental.findUnique({
    where: { id },
    include: {
      product: true,
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true
        }
      },
      payment: true
    }
  });

  // Jika rental tidak ditemukan, tampilkan halaman 404
  if (!rental) {
    notFound();
  }

  // Formatkan durasi untuk tampilan
  const formattedDuration = rental.duration
    ? rental.duration.replace('_HOURS', '').replace('x8', ' x 8 Jam')
    : '-';

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-3">
          <Link href="/admin/rentals">
            <Button variant="outline" className="dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">Kembali</Button>
          </Link>
          <h1 className="text-2xl font-bold dark:text-white">Detail Penyewaan</h1>
        </div>
        <div className="flex items-center gap-3">
          <Badge className={getStatusColor(rental.status)}>
            {getStatusName(rental.status)}
          </Badge>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon" className="dark:border-gray-700">
                <MoreVertical className="h-4 w-4 dark:text-gray-300" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="dark:bg-gray-800 dark:border-gray-700">
              {rental.status === "PENDING" && (
                <>
                  <DropdownMenuItem asChild className="dark:text-gray-300 dark:hover:bg-gray-700">
                    <Link href={`/admin/rentals/${rental.id}/confirm`}>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Konfirmasi Penyewaan
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild className="dark:text-gray-300 dark:hover:bg-gray-700">
                    <Link href={`/admin/rentals/${rental.id}/cancel`}>
                      <X className="mr-2 h-4 w-4" />
                      Batalkan
                    </Link>
                  </DropdownMenuItem>
                </>
              )}

              {rental.status === "CONFIRMED" && (
                <DropdownMenuItem asChild className="dark:text-gray-300 dark:hover:bg-gray-700">
                  <Link href={`/admin/operations/${rental.id}`}>
                    <PlayCircle className="mr-2 h-4 w-4" />
                    Kelola Operasi
                  </Link>
                </DropdownMenuItem>
              )}

              {["OPERATIONAL", "COMPLETED"].includes(rental.status) && (
                <DropdownMenuItem asChild className="dark:text-gray-300 dark:hover:bg-gray-700">
                  <Link href={`/admin/operations/${rental.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    Lihat Detail Operasi
                  </Link>
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator className="dark:border-gray-700" />

              <DropdownMenuItem asChild className="dark:text-gray-300 dark:hover:bg-gray-700">
                <Link href={`/admin/users/${rental.user.id}`}>
                  <User className="mr-2 h-4 w-4" />
                  Lihat Profil Pelanggan
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Informasi Penyewaan */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Informasi Penyewaan</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Data Produk */}
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Package className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="font-semibold text-gray-700">{rental.product.name}</p>
                    <p className="text-sm text-gray-600">{rental.product.capacity} KVA</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Calendar className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-500">Tanggal Mulai</p>
                    <p className="font-medium">{formatDate(rental.startDate)}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Calendar className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-500">Tanggal Selesai</p>
                    <p className="font-medium">{formatDate(rental.endDate)}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Clock className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-500">Waktu Kedatangan</p>
                    <p className="font-medium">{rental.arrivalTime || '-'}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Clock className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-500">Durasi</p>
                    <p className="font-medium">{formattedDuration}</p>
                  </div>
                </div>
              </div>

              {/* Lokasi dan Catatan */}
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <MapPin className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-500">Alamat Pengiriman</p>
                    <p className="font-medium">{rental.address || '-'}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <FileText className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-500">Catatan</p>
                    <p className="font-medium">{rental.notes || '-'}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Package className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-500">Jumlah Unit</p>
                    <p className="font-medium">{rental.quantity} unit</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Deskripsi Produk */}
            {rental.product.description && (
              <div className="mt-6 pt-6 border-t">
                <p className="text-sm text-gray-500 mb-2">Deskripsi Produk</p>
                <p>{rental.product.description}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Informasi Pelanggan */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Pelanggan</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <User className="h-5 w-5 text-gray-500 mt-0.5" />
                <div>
                  <p className="text-sm text-gray-500">Nama</p>
                  <p className="font-medium">{rental.user.name}</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Mail className="h-5 w-5 text-gray-500 mt-0.5" />
                <div>
                  <p className="text-sm text-gray-500">Email</p>
                  <p className="font-medium">{rental.user.email}</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Phone className="h-5 w-5 text-gray-500 mt-0.5" />
                <div>
                  <p className="text-sm text-gray-500">Telepon</p>
                  <p className="font-medium">{rental.user.phone || '-'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}