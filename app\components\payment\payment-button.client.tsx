"use client";

import { useState, useEffect } from "react";
import { Button } from "@/app/components/ui/button";
import { LuCreditCard } from "react-icons/lu";
import { useToast } from "@/lib/hooks/use-toast";
import { useRouter } from "next/navigation";


interface PaymentButtonProps {
  rentalId: string;
  amount: number;
  type: "deposit" | "remaining" | "full";
  label?: string;
  colorClass?: string;
  onSuccess?: (data: PaymentResponse) => void;
}

interface PaymentResponse {
  success: boolean;
  message: string;
  token?: string;
  paymentId?: string;
  redirectUrl?: string;
}

export function PaymentButton({
  rentalId,
  amount,
  type,
  label = "Bayar Sekarang",

  onSuccess
}: PaymentButtonProps) {
  const [loading, setLoading] = useState(false);
  const [snapLoaded, setSnapLoaded] = useState(false);
  const { showSuccess, showError } = useToast();
  const router = useRouter();

  // Load Midtrans Snap script
  useEffect(() => {
    const snapScript = 'https://app.sandbox.midtrans.com/snap/snap.js';
    const clientKey = process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY;

    if (!clientKey) {
      console.error('Midtrans client key not found');
      return;
    }

    const script = document.createElement('script');
    script.src = snapScript;
    script.setAttribute('data-client-key', clientKey);
    script.onload = () => {
      setSnapLoaded(true);
    };
    script.onerror = () => {
      console.error('Failed to load Midtrans Snap script');
      showError('Gagal memuat sistem pembayaran');
    };

    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, [showError]);

  const processPayment = async () => {
    if (!snapLoaded) {
      showError('Sistem pembayaran belum siap. Silakan coba lagi.');
      return;
    }

    try {
      setLoading(true);

      // Tentukan endpoint API berdasarkan tipe pembayaran
      const endpoint = type === "deposit"
        ? `/api/payments/deposit/${rentalId}`
        : type === "remaining"
          ? `/api/payments/remaining/${rentalId}`
          : `/api/payments/full/${rentalId}`;

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ amount }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Terjadi kesalahan dalam proses pembayaran");
      }

      const data = await response.json() as PaymentResponse;

      if (!data.token) {
        throw new Error("Token pembayaran tidak ditemukan");
      }

      // Buka Midtrans Snap popup
      window.snap?.pay(data.token, {
        onSuccess: (result) => {
          console.log('Payment success:', result);
          const successMessage = type === "deposit"
            ? "Pembayaran Berhasil. Deposit telah berhasil dibayar"
            : type === "remaining"
              ? "Pembayaran Berhasil. Pelunasan telah berhasil dibayar"
              : "Pembayaran Berhasil. Pembayaran telah berhasil dilakukan";

          showSuccess(successMessage);

          // Jika ada callback onSuccess, panggil
          if (onSuccess) {
            onSuccess({ ...data, success: true });
          } else {
            // Redirect ke halaman sukses
            router.push(`/user/payments/success?rentalId=${rentalId}&type=${type}`);
          }
        },
        onPending: (result) => {
          console.log('Payment pending:', result);
          showSuccess('Pembayaran sedang diproses. Silakan tunggu konfirmasi.');
        },
        onError: (result) => {
          console.error('Payment error:', result);
          showError('Pembayaran gagal. Silakan coba lagi.');
        },
        onClose: () => {
          console.log('Payment popup closed');
        }
      });

    } catch (error: unknown) {
      console.error("Error processing payment:", error);
      const errorMessage = error instanceof Error ? error.message : "Terjadi kesalahan dalam proses pembayaran";
      showError(`Pembayaran Gagal. ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onClick={processPayment}
      disabled={loading || !snapLoaded}
      loading={loading}
      variant={type === "deposit" ? "success" : "gradient"}
      size="mobile"
      className="text-white shadow-lg hover:shadow-xl"
    >
      {!loading && <LuCreditCard className="mr-2 h-4 w-4" />}
      {loading ? "Memproses..." : !snapLoaded ? "Memuat..." : label}
    </Button>
  );
}
