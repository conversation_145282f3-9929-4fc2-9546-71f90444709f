import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateRentalDurations() {
  try {
    console.log('Updating rental durations...');

    // Get all rentals that might have old duration format or missing duration
    const rentals = await prisma.rental.findMany({
      select: {
        id: true,
        duration: true,
        startDate: true,
        endDate: true
      }
    });

    console.log(`Found ${rentals.length} rentals to check`);

    let updatedCount = 0;

    for (const rental of rentals) {
      let newDuration = rental.duration;

      // If duration is null or empty, calculate from date difference
      if (!rental.duration) {
        const diffTime = Math.abs(new Date(rental.endDate).getTime() - new Date(rental.startDate).getTime());
        const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
        
        // Map hours to our duration constants
        if (diffHours <= 8) {
          newDuration = '1x8_HOURS';
        } else if (diffHours <= 16) {
          newDuration = '2x8_HOURS';
        } else if (diffHours <= 24) {
          newDuration = '3x8_HOURS';
        } else if (diffHours <= 32) {
          newDuration = '4x8_HOURS';
        } else if (diffHours <= 40) {
          newDuration = '5x8_HOURS';
        } else if (diffHours <= 48) {
          newDuration = '6x8_HOURS';
        } else if (diffHours <= 56) {
          newDuration = '7x8_HOURS';
        } else if (diffHours <= 64) {
          newDuration = '8x8_HOURS';
        } else if (diffHours <= 72) {
          newDuration = '9x8_HOURS';
        } else {
          newDuration = '10x8_HOURS';
        }
      }
      // If duration exists but in old format, convert it
      else if (rental.duration && !rental.duration.includes('x8_HOURS')) {
        // Handle various old formats
        if (rental.duration.includes('hari') || rental.duration.includes('day')) {
          const days = parseInt(rental.duration.replace(/[^0-9]/g, ''));
          if (!isNaN(days)) {
            const hours = days * 24;
            if (hours <= 8) {
              newDuration = '1x8_HOURS';
            } else if (hours <= 16) {
              newDuration = '2x8_HOURS';
            } else if (hours <= 24) {
              newDuration = '3x8_HOURS';
            } else if (hours <= 32) {
              newDuration = '4x8_HOURS';
            } else if (hours <= 40) {
              newDuration = '5x8_HOURS';
            } else if (hours <= 48) {
              newDuration = '6x8_HOURS';
            } else if (hours <= 56) {
              newDuration = '7x8_HOURS';
            } else if (hours <= 64) {
              newDuration = '8x8_HOURS';
            } else if (hours <= 72) {
              newDuration = '9x8_HOURS';
            } else {
              newDuration = '10x8_HOURS';
            }
          }
        }
      }

      // Update if duration changed
      if (newDuration !== rental.duration) {
        await prisma.rental.update({
          where: { id: rental.id },
          data: { duration: newDuration }
        });
        
        console.log(`Updated rental ${rental.id}: "${rental.duration}" -> "${newDuration}"`);
        updatedCount++;
      }
    }

    console.log(`Successfully updated ${updatedCount} rentals`);

  } catch (error) {
    console.error('Error updating rental durations:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateRentalDurations();
